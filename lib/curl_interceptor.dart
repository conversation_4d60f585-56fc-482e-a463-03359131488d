import 'package:dio/dio.dart';

/// 一个简单的 Dio 拦截器，用于将网络请求转换为 cURL 命令并打印到控制台。
/// 这在调试时非常有用，可以轻松地在终端中复现请求。
class CurlInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final curlCommand = _convertToCurl(options);
    print("--- cURL Command ---");
    print(curlCommand);
    print("--------------------");
    // 继续请求链
    super.onRequest(options, handler);
  }

  /// 将 Dio 的 RequestOptions 转换为 cURL 命令字符串。
  String _convertToCurl(RequestOptions options) {
    final List<String> components = ['curl -i'];

    // Method
    components.add('-X ${options.method.toUpperCase()}');

    // Headers
    options.headers.forEach((key, value) {
      // cURL 会自动计算 Content-Length，所以我们不需要手动添加
      if (key.toLowerCase() != 'content-length') {
        final header =
            '-H \'${_escapeSingleQuotes(key)}: ${_escapeSingleQuotes(value.toString())}\'';
        components.add(header);
      }
    });

    // URL
    components.add('\'${_escapeSingleQuotes(options.uri.toString())}\'');

    // Body
    if (options.data != null) {
      String data = options.data.toString();
      // 使用 --data-binary 来保留 body 中的换行符等特殊字符，这对于 ndjson 很重要
      components.add('--data-binary \'${_escapeSingleQuotes(data)}\'');
    }

    // 使用 ' \\' 连接每一行，使其更易读
    return components.join(' \\\n  ');
  }

  /// 一个符合 POSIX 标准的方法，用于转义包含在单引号内的字符串中的单引号。
  String _escapeSingleQuotes(String str) {
    return str.replaceAll('\'', '\'\\\'\'');
  }
} 