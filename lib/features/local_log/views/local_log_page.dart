import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tpa_log_reader/widgets/message_content_view.dart';
import 'package:tpa_log_reader/utils/log_mapping.dart';
import '../models/log_session.dart';
import '../services/log_parser.dart';
import 'dart:io';
import 'dart:convert';
import 'package:intl/intl.dart';
import '../models/history_item.dart';
import '../services/history_service.dart';

class LocalLogPage extends StatefulWidget {
  const LocalLogPage({super.key});

  @override
  State<LocalLogPage> createState() => _LocalLogPageState();
}

class _LocalLogPageState extends State<LocalLogPage> {
  final LogParserService _parserService = LogParserService();
  final HistoryService _historyService = HistoryService();

  bool _isLoading = false;
  Map<String, List<LogSession>> _sessionsByDate = {};
  String? _error;

  String? _selectedDate;
  LogSession? _selectedSession;
  Map<String, dynamic>? _deviceInfo;
  List<Map<String, dynamic>>? _currentLogEntries;
  List<Map<String, dynamic>>? _filteredLogEntries;
  bool _isParsingLogs = false;
  
  // 日志排序相关状态
  bool _isReversed = false;
  
  // 日志类型过滤
  String? _selectedLogType;
  final List<String> _logTypes = [
    '全部类型',
    '设备信息',
    '步骤',
    '指令',
    '日志',
    '错误'
  ];

  Directory? _tempDirectory;
  String? _selectedZipFileName;

  List<HistoryItem> _historyItems = [];

  @override
  void initState() {
    super.initState();
    _loadHistory();
    _selectedLogType = '错误'; // 默认选择错误日志
  }

  Future<void> _loadHistory() async {
    final history = await _historyService.getHistory();
    if (mounted) {
      setState(() => _historyItems = history);
    }
  }

  @override
  void dispose() {
    _cleanupTempDirectory();
    super.dispose();
  }

  Future<void> _cleanupTempDirectory() async {
    if (_tempDirectory != null && await _tempDirectory!.exists()) {
      await _tempDirectory!.delete(recursive: true);
    }
    _tempDirectory = null;
  }

  Future<void> _processZipFile(String path, String name) async {
    await _cleanupTempDirectory();

    setState(() {
      _isLoading = true;
      _error = null;
      _sessionsByDate = {};
      _selectedZipFileName = name;
      _selectedDate = null;
      _selectedSession = null;
      _deviceInfo = null;
      _currentLogEntries = null;
    });

    try {
      final logRootPath = await _parserService.unzipLogArchive(path);
      _tempDirectory = Directory(logRootPath).parent;
      await _scanDirectory(logRootPath);

      final historyItem = HistoryItem(
        path: path,
        fileName: name,
        lastAccessed: DateTime.now().millisecondsSinceEpoch,
      );
      await _historyService.addHistoryItem(historyItem);
      await _loadHistory();
      
    } catch (e) {
      setState(() => _error = '处理压缩包时发生错误: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _pickAndProcessZipFile() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['zip'],
    );
    if (result != null && result.files.single.path != null) {
      final file = result.files.single;
      await _processZipFile(file.path!, file.name);
    }
  }

  // 解析日期字符串为DateTime对象，用于排序
  DateTime _parseDate(String dateStr) {
    try {
      // 假设日期格式为 yyyy-M-d
      final parts = dateStr.split('-');
      if (parts.length == 3) {
        final year = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final day = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      print('日期解析错误: $e');
    }
    // 解析失败时返回最早的日期
    return DateTime(1970);
  }

  Future<void> _scanDirectory(String path) async {
    setState(() => _isLoading = true);
    try {
      final sessions = await _parserService.scanDirectoryForSessions(path);
      
      // 对日期键进行排序
      final sortedDates = sessions.keys.toList()
        ..sort((a, b) => _parseDate(b).compareTo(_parseDate(a))); // 降序排列，最新日期在前
      
      // 创建排序后的Map
      final sortedSessions = <String, List<LogSession>>{};
      for (final date in sortedDates) {
        sortedSessions[date] = sessions[date]!;
      }
      
      setState(() {
        _sessionsByDate = sortedSessions;
        if (sortedSessions.isNotEmpty) {
          _selectedDate = sortedSessions.keys.first; // 使用排序后的第一个日期
          _selectedSession = sortedSessions[_selectedDate]?.first;
          _fetchDeviceInfoAndLogs();
        } else {
          _error = "目录中未找到有效的日志会话。";
        }
      });
    } catch (e) {
      setState(() => _error = "扫描日志时发生错误: $e");
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchDeviceInfoAndLogs() async {
    if (_selectedSession == null) return;

    setState(() => _isParsingLogs = true);
    try {
      final logs = await _parserService.parseLogFile(_selectedSession!.path);
      final deviceInfoEntry = logs.firstWhere(
        (log) => log['type'] == 0,
        orElse: () => {},
      );
      setState(() {
        _currentLogEntries = logs;
        _filteredLogEntries = logs;
        _deviceInfo = deviceInfoEntry.isNotEmpty ? deviceInfoEntry['info'] : null;
      });
      
      // 应用过滤
      _filterLogs();
    } catch (e) {
      setState(() => _error = '解析日志文件时出错: $e');
    } finally {
      setState(() => _isParsingLogs = false);
    }
  }
  
  // 切换日志排序顺序
  void _toggleLogOrder() {
    setState(() {
      _isReversed = !_isReversed;
      if (_filteredLogEntries != null) {
        _filteredLogEntries = _filteredLogEntries!.reversed.toList();
      }
    });
  }
  
  // 判断日志是否为错误日志
  bool _isErrorLog(Map<String, dynamic> log) {
    final type = log['type']?.toString();

    // 设备信息、步骤、指令类型的错误判断
    if (type == '1') { // 步骤
      final step = log['step']?.toString();
      final description = stepToText[step];

      // 未知步骤视为错误
      if (description == null) return true;

      // 步骤101（脚本异常退出）明确标记为错误
      if (step == '101') return true;

      // 步骤100（脚本完成退出）不是错误，即使包含错误代码也是正常的退出状态码
      if (step == '100') return false;

    } else if (type == '2') { // 指令
      final cmd = log['cmd']?.toString();
      final description = cmdToText[cmd];
      if (description == null) return true; // 未知指令视为错误

    } else if (type == '3') { // 日志
      final info = log['info']?.toString();

      // 明确标记为错误的日志
      if (info == 'err') return true;

      // 日志类型在映射中找不到，视为错误
      if (logToText[info] == null) return true;

    } else if (type != '0') { // 除了设备信息外，未知类型都视为错误
      return true;
    }

    // 检查是否有错误代码（但排除步骤100的情况，因为它可能包含正常的退出状态码）
    if (log.containsKey('args') && log['args'] is Map && log['args'].containsKey('code')) {
      // 如果是步骤类型且是步骤100（脚本完成退出），则不视为错误
      if (type == '1' && log['step']?.toString() == '100') {
        return false;
      }
      return true;
    }

    return false;
  }
  
  // 过滤日志
  void _filterLogs() {
    if (_currentLogEntries == null) return;
    
    setState(() {
      if (_selectedLogType == null || _selectedLogType == '全部类型') {
        // 如果没有类型过滤，显示所有日志
        _filteredLogEntries = _currentLogEntries;
      } else {
        // 根据类型过滤日志
        _filteredLogEntries = _currentLogEntries!.where((log) {
          // 类型过滤
          final logType = log['type'];
          switch (_selectedLogType) {
            case '设备信息':
              return logType == 0;
            case '步骤':
              return logType == 1 && !_isErrorLog(log); // 排除错误日志
            case '指令':
              return logType == 2 && !_isErrorLog(log); // 排除错误日志
            case '日志':
              return logType == 3 && !_isErrorLog(log); // 排除错误日志
            case '错误':
              return _isErrorLog(log); // 使用统一的错误判断逻辑
            default:
              return true;
          }
        }).toList();
      }
    });
  }
  
  // 新增：更改日志类型过滤器
  void _onLogTypeChanged(String? newType) {
    if (newType != null && newType != _selectedLogType) {
      setState(() {
        _selectedLogType = newType;
      });
      _filterLogs();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日志压缩包分析器'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: '查看历史记录',
            onPressed: _showHistoryDialog,
          ),
          IconButton(
            icon: const Icon(Icons.archive_outlined),
            tooltip: '选择日志压缩包',
            onPressed: _pickAndProcessZipFile,
          ),
        ],
      ),
      backgroundColor: const Color(0xFFF8F9FA),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_selectedZipFileName == null) return _buildEmptyState();
    if (_isLoading) return const Center(child: CircularProgressIndicator());
    if (_error != null) return Center(child: Text(_error!));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 筛选卡片
          _buildFiltersCard(),
          const SizedBox(height: 16),
          if (_selectedSession?.imagePaths.isNotEmpty ?? false)
            _buildImagesCard(_selectedSession!.imagePaths),
          const SizedBox(height: 16),
          _buildLogsView(),
        ],
      ),
    );
  }

  Widget _buildFiltersCard() {
    final dates = _sessionsByDate.keys.toList();
    final sessionsForSelectedDate = _selectedDate != null ? _sessionsByDate[_selectedDate]! : <LogSession>[];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // 日期选择
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedDate,
                items: dates.map((d) => DropdownMenuItem(value: d, child: Text(d))).toList(),
                onChanged: (date) {
                  if (date == null) return;
                  setState(() {
                    _selectedDate = date;
                    _selectedSession = _sessionsByDate[date]?.first;
                    _fetchDeviceInfoAndLogs();
                  });
                },
                decoration: const InputDecoration(labelText: '选择日期', border: OutlineInputBorder()),
              ),
            ),
            const SizedBox(width: 16),
            
            // 会话选择
            Expanded(
              child: DropdownButtonFormField<LogSession>(
                value: _selectedSession,
                items: sessionsForSelectedDate.map((s) => DropdownMenuItem(value: s, child: Text(s.displayName))).toList(),
                onChanged: (session) {
                  if (session == null) return;
                  setState(() {
                    _selectedSession = session;
                    _fetchDeviceInfoAndLogs();
                  });
                },
                decoration: const InputDecoration(labelText: '选择会话', border: OutlineInputBorder()),
              ),
            ),
            const SizedBox(width: 16),
            
            // 日志类型过滤
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedLogType,
                items: _logTypes.map((type) => 
                  DropdownMenuItem(value: type, child: Text(type))
                ).toList(),
                onChanged: _onLogTypeChanged,
                decoration: const InputDecoration(
                  labelText: '日志类型',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceInfoCard(Map<String, dynamic> info) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('设备信息', style: Theme.of(context).textTheme.titleLarge),
                Chip(
                  avatar: const Icon(Icons.archive, size: 16),
                  label: Text(_selectedZipFileName ?? '未知文件'),
                )
              ],
            ),
            const Divider(height: 24),
            Wrap(
              spacing: 24.0,
              runSpacing: 12.0,
              children: info.entries.map((entry) {
                return RichText(
                  text: TextSpan(
                    style: Theme.of(context).textTheme.bodyMedium,
                    children: [
                      TextSpan(text: '${entry.key}: ', style: const TextStyle(color: Colors.grey)),
                      TextSpan(text: '${entry.value}', style: const TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesCard(List<String> imagePaths) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('会话截图', style: Theme.of(context).textTheme.titleLarge),
            const Divider(height: 24),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: imagePaths.length,
                itemBuilder: (context, index) {
                  final imagePath = imagePaths[index];
                  return GestureDetector(
                    onTap: () => _showImageDialog(context, imagePaths, index),
                    child: Card(
                      clipBehavior: Clip.antiAlias,
                      child: Image.file(
                        File(imagePath),
                        width: 100,
                        fit: BoxFit.cover,
                        frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                          if (wasSynchronouslyLoaded) return child;
                          return AnimatedOpacity(
                            opacity: frame == null ? 0 : 1,
                            duration: const Duration(seconds: 1),
                            curve: Curves.easeOut,
                            child: child,
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return const SizedBox(
                            width: 100,
                            child: Icon(Icons.error_outline, color: Colors.red),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showImageDialog(BuildContext context, List<String> imagePaths, int initialIndex) {
    showDialog(
      context: context,
      builder: (context) => _ImageCarouselDialog(
        imagePaths: imagePaths,
        initialIndex: initialIndex,
      ),
    );
  }

  Widget _buildLogsView() {
    if (_isParsingLogs) {
      return const Center(child: Padding(padding: EdgeInsets.all(32.0), child: CircularProgressIndicator()));
    }
    if (_currentLogEntries == null) {
      return const Card(child: Padding(padding: EdgeInsets.all(32.0), child: Center(child: Text('没有可显示的日志。'))));
    }
    return Card(
      child: Column(
        children: [
          // 添加日志统计信息
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                const Icon(Icons.info_outline, size: 16),
                const SizedBox(width: 8),
                Text('共 ${_filteredLogEntries!.length}/${_currentLogEntries!.length} 条日志', 
                  style: const TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(width: 16),
                Text('错误: ${_filteredLogEntries!.where((log) => _isErrorLog(log)).length} 条',
                  style: const TextStyle(color: Colors.red)),
                const Spacer(),
                TextButton.icon(
                  icon: Icon(_isReversed ? Icons.arrow_downward : Icons.arrow_upward),
                  label: Text(_isReversed ? '倒序' : '顺序'),
                  onPressed: _toggleLogOrder,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          // 日志内容
          MessageContentView(
            content: _filteredLogEntries!,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
     return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.upload_file_outlined, size: 80, color: Colors.grey),
          const SizedBox(height: 16),
          const Text('请选择日志压缩包', style: TextStyle(fontSize: 20)),
           const SizedBox(height: 8),
          Text('点击右上角的图标开始分析', style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }

  void _showHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('历史记录'),
          content: SizedBox(
            width: double.maxFinite,
            child: _historyItems.isEmpty
                ? const Center(child: Text('没有历史记录。'))
                : ListView.builder(
                    itemCount: _historyItems.length,
                    itemBuilder: (context, index) {
                      final item = _historyItems[index];
                      return ListTile(
                        title: Text(item.fileName),
                        subtitle: Text(DateFormat.yMMMd().add_jms().format(DateTime.fromMillisecondsSinceEpoch(item.lastAccessed))),
                        onTap: () async {
                          Navigator.of(context).pop();
                          final file = File(item.path);
                          if (await file.exists()) {
                            _processZipFile(item.path, item.fileName);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content: Text('文件已不存在: ${item.path}'),
                              action: SnackBarAction(
                                label: '移除记录',
                                onPressed: () async {
                                  await _historyService.removeHistoryItem(item.path);
                                  _loadHistory();
                                },
                              ),
                            ));
                          }
                        },
                        trailing: IconButton(
                          icon: const Icon(Icons.delete_outline, color: Colors.red),
                          onPressed: () async {
                            await _historyService.removeHistoryItem(item.path);
                            _loadHistory();
                            Navigator.of(context).pop();
                            _showHistoryDialog();
                          },
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        );
      },
    );
  }
}

class _ImageCarouselDialog extends StatefulWidget {
  final List<String> imagePaths;
  final int initialIndex;

  const _ImageCarouselDialog({
    required this.imagePaths,
    required this.initialIndex,
  });

  @override
  State<_ImageCarouselDialog> createState() => _ImageCarouselDialogState();
}

class _ImageCarouselDialogState extends State<_ImageCarouselDialog> {
  late final PageController _pageController;
  late int _currentIndex;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialIndex);
    _currentIndex = widget.initialIndex;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_focusNode);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _previousImage() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _nextImage() {
    if (_currentIndex < widget.imagePaths.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: KeyboardListener(
        focusNode: _focusNode,
        onKeyEvent: (event) {
          if (event is! KeyDownEvent) return;
          if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
            _previousImage();
          } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
            _nextImage();
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(
                height: 500, // Constrain height for PageView
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: widget.imagePaths.length,
                  onPageChanged: _onPageChanged,
                  itemBuilder: (context, index) {
                    return Image.file(
                      File(widget.imagePaths[index]),
                      fit: BoxFit.contain,
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back_ios),
                    onPressed: _currentIndex > 0 ? _previousImage : null,
                  ),
                  Text('${_currentIndex + 1} / ${widget.imagePaths.length}'),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward_ios),
                    onPressed: _currentIndex < widget.imagePaths.length - 1 ? _nextImage : null,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              TextButton(
                child: const Text("关闭"),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}