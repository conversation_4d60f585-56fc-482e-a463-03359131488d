import 'package:flutter/material.dart';
import 'package:tpa_log_reader/kibana_service.dart';
import '../models/log_entry.dart';
import '../models/filter.dart';
import '../models/query_ast.dart';
import '../utils/query_parser.dart';
import '../widgets/developer_stats_bar.dart';
import '../widgets/filter_pill_bar.dart';
import '../widgets/log_search_bar.dart';
import '../widgets/timeline_log_item.dart';
import '../widgets/time_range_selector.dart';
import '../../features/local_log/views/local_log_page.dart';

class LogViewerPage extends StatefulWidget {
  const LogViewerPage({super.key});

  @override
  State<LogViewerPage> createState() => _LogViewerPageState();
}

class _LogViewerPageState extends State<LogViewerPage> {
  final KibanaService _kibanaService = KibanaService();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false;
  bool _isLoadingMore = false;
  List<LogEntry> _filteredLogs = [];
  int _totalLogs = 0;
  List<dynamic>? _lastSortValues;
  String? _error;

  // --- 新增: 页面大小状态 ---
  int _pageSize = 100;
  final List<int> _availablePageSizes = [50, 100, 200, 500, 1000];

  final TextEditingController _queryController = TextEditingController();
  List<Filter> _activeFilters = [];
  QueryNode? _queryAst;

  // --- State for index patterns is now dynamic ---
  List<String> _availableIndexPatterns = [];
  String? _currentIndexPattern;
  bool _isLoadingIndexPatterns = true;

  // --- Initialize with default values to prevent LateInitializationError ---
  late DateTime _startTime = DateTime.now().subtract(const Duration(minutes: 5));
  late DateTime _endTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializePage();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _queryController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        !_isLoadingMore &&
        _filteredLogs.length < _totalLogs) {
      _executeSearch(isLoadMore: true);
    }
  }

  Future<void> _initializePage() async {
    await _fetchIndexPatterns();
    if (_currentIndexPattern != null) {
      await _executeSearch(isInitialSearch: true);
    }
  }

  Future<void> _fetchIndexPatterns() async {
    setState(() => _isLoadingIndexPatterns = true);
    try {
      final patterns = await _kibanaService.fetchIndexPatterns();
      patterns.sort();
      setState(() {
        _availableIndexPatterns = patterns;
        if (patterns.isNotEmpty) {
          // --- 默认项目选择逻辑 ---
          // 查找是否有 'p1-pro-work-wechat-magic-*' 格式的项目
          final preferredProject = patterns.firstWhere(
            (p) => p.startsWith('p1-pro-work-wechat-magic-'),
            orElse: () => '', // 如果没找到，返回空字符串
          );

          if (preferredProject.isNotEmpty) {
            _currentIndexPattern = preferredProject;
          } else {
            // 如果没有找到，则使用列表中的第一个作为默认值
            _currentIndexPattern = patterns.first;
          }
        }
      });
    } catch (e) {
      print("Failed to fetch index patterns in UI: $e");
    } finally {
      setState(() => _isLoadingIndexPatterns = false);
    }
  }

  Future<void> _executeSearch({
    bool isInitialSearch = false,
    bool isLoadMore = false,
  }) async {
    if (_currentIndexPattern == null) {
      print("No index pattern selected. Aborting search.");
      return;
    }

    if (_isLoading || _isLoadingMore) return;

    if (isInitialSearch) {
      setState(() {
        _activeFilters.clear();
        _queryController.clear();
        _queryAst = null;
      });
    }

    setState(() {
      if (isLoadMore) {
        _isLoadingMore = true;
      } else {
        _isLoading = true;
        _error = null;
        _filteredLogs.clear();
        _lastSortValues = null;
        _totalLogs = 0;
      }
    });

    try {
      final response = await _kibanaService.fetchLogs(
        queryAst: _queryAst,
        startTime: _startTime,
        endTime: _endTime,
        indexPattern: _currentIndexPattern!,
        searchAfter: isLoadMore ? _lastSortValues : null,
        pageSize: _pageSize,
      );
      
      final List<dynamic> fetchedLogMaps = response['logs'];
      final int total = response['total'];
      final List<dynamic>? lastSort = response['lastSortValues'];

      final parsedLogs = fetchedLogMaps.map((logMap) => LogEntry.fromJson(logMap)).toList();

      setState(() {
        if (isLoadMore) {
          _filteredLogs.addAll(parsedLogs);
        } else {
          _filteredLogs = parsedLogs;
        }
        _totalLogs = total;
        _lastSortValues = lastSort;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        if (!isLoadMore) {
          _filteredLogs.clear();
        }
      });
      print('--- LOG FETCHING FAILED ---');
      print(e);
      print('---------------------------');
    } finally {
      setState(() {
        if (isLoadMore) {
          _isLoadingMore = false;
        } else {
          _isLoading = false;
        }
      });
    }
  }

  void _parseAndSetFilters() {
    // 在解析前，先对查询字符串进行净化，移除可能导致解析器卡死的协议头
    final sanitizedQuery = _queryController.text.replaceAll(RegExp(r'https?:\/\/'), '');
    try {
      final parsedResult = QueryParser.parse(sanitizedQuery);
      setState(() {
        // 从解析结果中获取扁平化的过滤器列表用于UI，以及AST用于查询
        _activeFilters = parsedResult.filters;
        _queryAst = parsedResult.astRoot;
        _error = null; // 清除之前的错误
      });
      _executeSearch();
    } on QueryParseException catch (e) {
      setState(() {
        _error = '查询语法错误: ${e.message}';
        _isLoading = false;
      });
    }
  }

  void _removeFilter(Filter filter) {
    setState(() {
      _activeFilters.remove(filter);
      
      // Basic implementation to remove filter from query string.
      // This is not robust for complex nested queries but works for simple cases.
      final filterText1 = '${filter.key}${filter.operator}"${filter.value}"';
      final filterText2 = "${filter.key}${filter.operator}${filter.value}";
      
      String newQuery = _queryController.text;
      if (newQuery.contains(filterText1)) {
        newQuery = newQuery.replaceFirst(filterText1, '');
      } else if (newQuery.contains(filterText2)) {
        newQuery = newQuery.replaceFirst(filterText2, '');
      }

      // Clean up dangling operators
      newQuery = newQuery.replaceAll(RegExp(r'\s+(AND|OR)\s+(?=(AND|OR|$|\)))'), ' ');
      newQuery = newQuery.replaceAll(RegExp(r'(^|\()\s*(AND|OR)\s+'), ' ');
      
      _queryController.text = newQuery.trim();
      _parseAndSetFilters();
    });
  }

  void _onClearSearch() {
    setState(() {
      _queryController.clear();
      _activeFilters.clear();
      _queryAst = null;
    });
    _executeSearch();
  }

  void _onFilter(String field, String value, bool isExclusion) {
    // 如果值包含空格，则用引号括起来
    final valueString = value.contains(' ') ? '"$value"' : value;
    final newFilter = isExclusion
        ? 'NOT $field: $valueString'
        : '$field: $valueString';

    final currentQuery = _queryController.text.trim();
    
    setState(() {
      if (currentQuery.isEmpty) {
        _queryController.text = newFilter;
      } else {
        _queryController.text = '$currentQuery AND $newFilter';
      }
      // 关闭可能已展开的卡片
      // (理想情况下需要一种方式来通知 TimelineLogItem 收起)
    });
    
    _parseAndSetFilters();
  }

  void _onTimeRangeChanged(DateTime newStart, DateTime newEnd) {
    setState(() {
      _startTime = newStart;
      _endTime = newEnd;
    });
    _executeSearch();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kibana 日志查看器'),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: _isLoadingIndexPatterns
                    ? const Center(
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 3,
                          ),
                        ),
                      )
                    : DropdownButton<String>(
                        value: _currentIndexPattern,
                        style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                        items: _availableIndexPatterns.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value, style: const TextStyle(color: Colors.black)),
                          );
                        }).toList(),
                        onChanged: (newValue) {
                          if (newValue != null && newValue != _currentIndexPattern) {
                            setState(() {
                              _currentIndexPattern = newValue;
                            });
                            _executeSearch(isInitialSearch: true);
                          }
                        },
                        underline: Container(),
                        icon: const Icon(Icons.arrow_drop_down_circle_outlined, color: Colors.black),
                        dropdownColor: Colors.white,
                      ),
          ),
          IconButton(
            icon: const Icon(Icons.description_outlined),
            tooltip: '读取本地日志',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const LocalLogPage()),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
            onPressed: () {
              _executeSearch(isInitialSearch: true);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 8, 8, 0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TimeRangeSelector(
                  startTime: _startTime,
                  endTime: _endTime,
                  onTimeRangeSelected: _onTimeRangeChanged,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LogSearchBar(
                    controller: _queryController,
                    isLoading: _isLoading,
                    onSearch: _parseAndSetFilters,
                    onClear: _onClearSearch,
                  ),
                ),
              ],
            ),
          ),
          // --- Filter Pills have been removed as per user request to simplify UI ---
          // if (_activeFilters.isNotEmpty)
          //   Padding(
          //     padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
          //     child: FilterPillBar(
          //       filters: _activeFilters,
          //       onRemoveFilter: _removeFilter,
          //     ),
          //   ),
          Expanded(
            child: _buildBody(),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null && !_isLoadingMore) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              const Text('加载失败', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('$_error', textAlign: TextAlign.center, style: const TextStyle(color: Colors.red)),
            ],
          ),
        ),
      );
    }

    if (_filteredLogs.isEmpty) {
      final bool hasFilters = _activeFilters.isNotEmpty || (_queryController.text.trim().isNotEmpty);
      final icon = hasFilters ? Icons.filter_alt_off_outlined : Icons.search_off;
      final title = hasFilters ? '无匹配日志' : '无日志';
      final message = hasFilters
          ? '没有匹配当前筛选条件的日志。\n请尝试调整或移除筛选器。'
          : '在指定的时间范围和项目中未找到任何日志。';

      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 24),
              Text(title, style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.grey[600])),
              const SizedBox(height: 12),
              Text(message, textAlign: TextAlign.center, style: TextStyle(fontSize: 16, color: Colors.grey[500])),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DeveloperStatsBar(
          logs: _filteredLogs, 
          onRefresh: () => _executeSearch(isInitialSearch: true),
          pageSize: _pageSize,
          availablePageSizes: _availablePageSizes,
          onPageSizeChanged: (newValue) {
            if (newValue != null && newValue != _pageSize) {
              setState(() {
                _pageSize = newValue;
              });
              _executeSearch(isInitialSearch: true);
            }
          },
        ),
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 50),
            itemCount: _filteredLogs.length + (_isLoadingMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _filteredLogs.length) {
                return const Padding(
                  padding: EdgeInsets.symmetric(vertical: 20),
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              return TimelineLogItem(
                log: _filteredLogs[index],
                onFilter: _onFilter,
                isLast: index == _filteredLogs.length - 1,
              );
            },
          ),
        ),
      ],
    );
  }
}